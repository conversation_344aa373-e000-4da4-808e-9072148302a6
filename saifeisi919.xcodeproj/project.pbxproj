// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		241574F12E7DAABD0015BB82 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 241574D92E7DAABC0015BB82 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 241574E02E7DAABC0015BB82;
			remoteInfo = saifeisi919;
		};
		241574FB2E7DAABD0015BB82 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 241574D92E7DAABC0015BB82 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 241574E02E7DAABC0015BB82;
			remoteInfo = saifeisi919;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		241574E12E7DAABC0015BB82 /* saifeisi919.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = saifeisi919.app; sourceTree = BUILT_PRODUCTS_DIR; };
		241574F02E7DAABD0015BB82 /* saifeisi919Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = saifeisi919Tests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		241574FA2E7DAABD0015BB82 /* saifeisi919UITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = saifeisi919UITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		241574E32E7DAABC0015BB82 /* saifeisi919 */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = saifeisi919;
			sourceTree = "<group>";
		};
		241574F32E7DAABD0015BB82 /* saifeisi919Tests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = saifeisi919Tests;
			sourceTree = "<group>";
		};
		241574FD2E7DAABD0015BB82 /* saifeisi919UITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = saifeisi919UITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		241574DE2E7DAABC0015BB82 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		241574ED2E7DAABD0015BB82 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		241574F72E7DAABD0015BB82 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		241574D82E7DAABC0015BB82 = {
			isa = PBXGroup;
			children = (
				241574E32E7DAABC0015BB82 /* saifeisi919 */,
				241574F32E7DAABD0015BB82 /* saifeisi919Tests */,
				241574FD2E7DAABD0015BB82 /* saifeisi919UITests */,
				241574E22E7DAABC0015BB82 /* Products */,
			);
			sourceTree = "<group>";
		};
		241574E22E7DAABC0015BB82 /* Products */ = {
			isa = PBXGroup;
			children = (
				241574E12E7DAABC0015BB82 /* saifeisi919.app */,
				241574F02E7DAABD0015BB82 /* saifeisi919Tests.xctest */,
				241574FA2E7DAABD0015BB82 /* saifeisi919UITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		241574E02E7DAABC0015BB82 /* saifeisi919 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 241575042E7DAABD0015BB82 /* Build configuration list for PBXNativeTarget "saifeisi919" */;
			buildPhases = (
				241574DD2E7DAABC0015BB82 /* Sources */,
				241574DE2E7DAABC0015BB82 /* Frameworks */,
				241574DF2E7DAABC0015BB82 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				241574E32E7DAABC0015BB82 /* saifeisi919 */,
			);
			name = saifeisi919;
			packageProductDependencies = (
			);
			productName = saifeisi919;
			productReference = 241574E12E7DAABC0015BB82 /* saifeisi919.app */;
			productType = "com.apple.product-type.application";
		};
		241574EF2E7DAABD0015BB82 /* saifeisi919Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 241575072E7DAABD0015BB82 /* Build configuration list for PBXNativeTarget "saifeisi919Tests" */;
			buildPhases = (
				241574EC2E7DAABD0015BB82 /* Sources */,
				241574ED2E7DAABD0015BB82 /* Frameworks */,
				241574EE2E7DAABD0015BB82 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				241574F22E7DAABD0015BB82 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				241574F32E7DAABD0015BB82 /* saifeisi919Tests */,
			);
			name = saifeisi919Tests;
			packageProductDependencies = (
			);
			productName = saifeisi919Tests;
			productReference = 241574F02E7DAABD0015BB82 /* saifeisi919Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		241574F92E7DAABD0015BB82 /* saifeisi919UITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2415750A2E7DAABD0015BB82 /* Build configuration list for PBXNativeTarget "saifeisi919UITests" */;
			buildPhases = (
				241574F62E7DAABD0015BB82 /* Sources */,
				241574F72E7DAABD0015BB82 /* Frameworks */,
				241574F82E7DAABD0015BB82 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				241574FC2E7DAABD0015BB82 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				241574FD2E7DAABD0015BB82 /* saifeisi919UITests */,
			);
			name = saifeisi919UITests;
			packageProductDependencies = (
			);
			productName = saifeisi919UITests;
			productReference = 241574FA2E7DAABD0015BB82 /* saifeisi919UITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		241574D92E7DAABC0015BB82 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					241574E02E7DAABC0015BB82 = {
						CreatedOnToolsVersion = 16.4;
					};
					241574EF2E7DAABD0015BB82 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 241574E02E7DAABC0015BB82;
					};
					241574F92E7DAABD0015BB82 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 241574E02E7DAABC0015BB82;
					};
				};
			};
			buildConfigurationList = 241574DC2E7DAABC0015BB82 /* Build configuration list for PBXProject "saifeisi919" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 241574D82E7DAABC0015BB82;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 241574E22E7DAABC0015BB82 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				241574E02E7DAABC0015BB82 /* saifeisi919 */,
				241574EF2E7DAABD0015BB82 /* saifeisi919Tests */,
				241574F92E7DAABD0015BB82 /* saifeisi919UITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		241574DF2E7DAABC0015BB82 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		241574EE2E7DAABD0015BB82 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		241574F82E7DAABD0015BB82 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		241574DD2E7DAABC0015BB82 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		241574EC2E7DAABD0015BB82 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		241574F62E7DAABD0015BB82 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		241574F22E7DAABD0015BB82 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 241574E02E7DAABC0015BB82 /* saifeisi919 */;
			targetProxy = 241574F12E7DAABD0015BB82 /* PBXContainerItemProxy */;
		};
		241574FC2E7DAABD0015BB82 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 241574E02E7DAABC0015BB82 /* saifeisi919 */;
			targetProxy = 241574FB2E7DAABD0015BB82 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		241575022E7DAABD0015BB82 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		241575032E7DAABD0015BB82 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		241575052E7DAABD0015BB82 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.saifeisi919.test.saifeisi919;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		241575062E7DAABD0015BB82 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.saifeisi919.test.saifeisi919;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		241575082E7DAABD0015BB82 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.saifeisi919.test.saifeisi919Tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/saifeisi919.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/saifeisi919";
			};
			name = Debug;
		};
		241575092E7DAABD0015BB82 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.saifeisi919.test.saifeisi919Tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/saifeisi919.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/saifeisi919";
			};
			name = Release;
		};
		2415750B2E7DAABD0015BB82 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.saifeisi919.test.saifeisi919UITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = saifeisi919;
			};
			name = Debug;
		};
		2415750C2E7DAABD0015BB82 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.saifeisi919.test.saifeisi919UITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = saifeisi919;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		241574DC2E7DAABC0015BB82 /* Build configuration list for PBXProject "saifeisi919" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				241575022E7DAABD0015BB82 /* Debug */,
				241575032E7DAABD0015BB82 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		241575042E7DAABD0015BB82 /* Build configuration list for PBXNativeTarget "saifeisi919" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				241575052E7DAABD0015BB82 /* Debug */,
				241575062E7DAABD0015BB82 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		241575072E7DAABD0015BB82 /* Build configuration list for PBXNativeTarget "saifeisi919Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				241575082E7DAABD0015BB82 /* Debug */,
				241575092E7DAABD0015BB82 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2415750A2E7DAABD0015BB82 /* Build configuration list for PBXNativeTarget "saifeisi919UITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2415750B2E7DAABD0015BB82 /* Debug */,
				2415750C2E7DAABD0015BB82 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 241574D92E7DAABC0015BB82 /* Project object */;
}

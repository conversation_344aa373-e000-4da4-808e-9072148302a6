//
//  LaunchScreen.swift
//  saifeisi919
//
//  Created by 赛飞斯 on 2025/9/19.
//

import SwiftUI

struct LaunchScreen: View {
    @State private var isAnimating = false
    @State private var opacity = 0.0
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景渐变
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.red.opacity(0.1),
                        Color.blue.opacity(0.1)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: 30) {
                    Spacer()
                    
                    // 应用图标动画
                    VStack(spacing: 20) {
                        // 模拟双色球图标
                        HStack(spacing: 8) {
                            ForEach(1...6, id: \.self) { index in
                                Circle()
                                    .fill(Color.red)
                                    .frame(width: 25, height: 25)
                                    .scaleEffect(isAnimating ? 1.2 : 1.0)
                                    .animation(
                                        .easeInOut(duration: 0.6)
                                        .repeatForever(autoreverses: true)
                                        .delay(Double(index) * 0.1),
                                        value: isAnimating
                                    )
                            }
                            
                            Circle()
                                .fill(Color.blue)
                                .frame(width: 25, height: 25)
                                .scaleEffect(isAnimating ? 1.2 : 1.0)
                                .animation(
                                    .easeInOut(duration: 0.6)
                                    .repeatForever(autoreverses: true)
                                    .delay(0.7),
                                    value: isAnimating
                                )
                        }
                        
                        Text("丰减壮生成器")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                            .opacity(opacity)
                    }
                    
                    Spacer()
                    
                    // 底部文字
                    VStack(spacing: 8) {
                        Text("多种彩票 · 随机生成")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .opacity(opacity)
                        
                        Text("适配 iPhone 14 Pro")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .opacity(opacity * 0.7)
                    }
                    .padding(.bottom, geometry.safeAreaInsets.bottom > 0 ? 20 : 40)
                }
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 1.0)) {
                isAnimating = true
                opacity = 1.0
            }
        }
    }
}

#Preview {
    LaunchScreen()
}

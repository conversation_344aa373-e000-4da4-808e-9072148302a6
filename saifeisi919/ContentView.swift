//
//  ContentView.swift
//  saifeisi919
//
//  Created by 赛飞斯 on 2025/9/19.
//

import SwiftUI
import SwiftData

enum LotteryType: String, CaseIterable {
    case shuangSeQiu = "双色球"
    case daLeTou = "大乐透"
    case kuaiLe8 = "快乐8"
    case liuHeCai = "六合彩"
}

struct LotteryNumber {
    let type: LotteryType
    let redBalls: [Int]
    let blueBall: Int?
    let specialBalls: [Int]? // 用于大乐透的后区号码
    let timestamp: Date
}

struct ContentView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var items: [Item]

    @State private var selectedLotteryType: LotteryType = .shuangSeQiu
    @State private var currentNumbers: LotteryNumber?
    @State private var isGenerating = false
    @State private var generatedHistory: [LotteryNumber] = []

    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                ScrollView {
                    VStack(spacing: 20) {
                        // 标题 - 修改为新的应用名称
                        VStack(spacing: 8) {
                            Text("丰减壮生成器")
                                .font(.largeTitle)
                                .fontWeight(.bold)
                                .foregroundColor(.primary)

                            Text("丰收减损 运气壮壮")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        .padding(.top, geometry.safeAreaInsets.top > 50 ? 10 : 20)
                        .frame(maxWidth: .infinity) // 确保居中对齐

                        // 彩票类型选择器
                        VStack(spacing: 12) {
                            Text("选择彩票类型")
                                .font(.headline)
                                .foregroundColor(.primary)

                            Picker("彩票类型", selection: $selectedLotteryType) {
                                ForEach(LotteryType.allCases, id: \.self) { type in
                                    Text(type.rawValue).tag(type)
                                }
                            }
                            .pickerStyle(SegmentedPickerStyle())
                            .padding(.horizontal, 16)
                        }
                        .padding(.horizontal, 20)

                        // 当前号码显示区域 - 优化布局和居中对齐
                        VStack(spacing: 20) {
                            if let numbers = currentNumbers {
                                VStack(spacing: 18) {
                                    Text("\(numbers.type.rawValue)本期号码")
                                        .font(.title2)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.secondary)

                                    // 根据不同彩票类型显示号码
                                    switch numbers.type {
                                    case .shuangSeQiu:
                                        shuangSeQiuNumbersView(numbers: numbers, geometry: geometry)
                                    case .daLeTou:
                                        daLeTouNumbersView(numbers: numbers, geometry: geometry)
                                    case .kuaiLe8:
                                        kuaiLe8NumbersView(numbers: numbers, geometry: geometry)
                                    case .liuHeCai:
                                        liuHeCaiNumbersView(numbers: numbers, geometry: geometry)
                                    }

                                    Text("生成时间: \(numbers.timestamp, format: Date.FormatStyle(date: .abbreviated, time: .shortened))")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                .padding(20)
                                .frame(maxWidth: .infinity) // 确保居中
                                .background(
                                    RoundedRectangle(cornerRadius: 20)
                                        .fill(Color(.systemBackground))
                                        .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
                                )
                            } else {
                                VStack(spacing: 20) {
                                    Image(systemName: "dice.fill")
                                        .font(.system(size: 70))
                                        .foregroundColor(.gray.opacity(0.6))

                                    VStack(spacing: 8) {
                                        Text("\(selectedLotteryType.rawValue)号码生成器")
                                            .font(.title2)
                                            .fontWeight(.semibold)
                                            .foregroundColor(.primary)

                                        Text("点击下方按钮生成随机号码")
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                            .multilineTextAlignment(.center)
                                    }
                                }
                                .padding(30)
                                .frame(height: 220)
                                .frame(maxWidth: .infinity)
                                .background(
                                    RoundedRectangle(cornerRadius: 20)
                                        .fill(Color(.systemGray6))
                                )
                            }
                        }
                        .padding(.horizontal, 20)

                        // 生成按钮 - 优化iPhone 14 Pro体验
                        Button(action: generateNumbers) {
                            HStack(spacing: 12) {
                                if isGenerating {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(0.9)
                                } else {
                                    Image(systemName: "dice.fill")
                                        .font(.title2)
                                }

                                Text(isGenerating ? "生成中..." : "生成\(selectedLotteryType.rawValue)")
                                    .font(.title2)
                                    .fontWeight(.semibold)
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 60)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.red.opacity(0.9),
                                        Color.red,
                                        Color.blue,
                                        Color.blue.opacity(0.9)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .cornerRadius(18)
                            .shadow(color: .black.opacity(0.25), radius: 8, x: 0, y: 4)
                            .scaleEffect(isGenerating ? 0.95 : 1.0)
                            .animation(.easeInOut(duration: 0.1), value: isGenerating)
                        }
                        .disabled(isGenerating)
                        .padding(.horizontal, 20)

                        // 历史记录 - 适配iPhone 14 Pro
                        if !generatedHistory.isEmpty {
                            VStack(alignment: .leading, spacing: 15) {
                                HStack {
                                    Text("历史记录")
                                        .font(.title3)
                                        .fontWeight(.semibold)

                                    Spacer()

                                    Text("共\(generatedHistory.count)注")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                .padding(.horizontal, 20)

                                LazyVStack(spacing: 10) {
                                    ForEach(Array(generatedHistory.enumerated().reversed()), id: \.offset) { index, lottery in
                                        HistoryRowView(lottery: lottery, index: generatedHistory.count - index)
                                    }
                                }
                                .padding(.horizontal, 20)

                                // 返回首页按钮
                                Button(action: {
                                    withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                        currentNumbers = nil
                                        // 可选：清空历史记录
                                        // generatedHistory.removeAll()
                                    }
                                }) {
                                    HStack(spacing: 8) {
                                        Image(systemName: "house.fill")
                                            .font(.title3)
                                        Text("返回首页")
                                            .font(.title3)
                                            .fontWeight(.medium)
                                    }
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 50)
                                    .background(
                                        LinearGradient(
                                            gradient: Gradient(colors: [Color.gray.opacity(0.8), Color.gray]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .cornerRadius(15)
                                    .shadow(color: .black.opacity(0.2), radius: 5, x: 0, y: 2)
                                }
                                .padding(.horizontal, 20)
                                .padding(.top, 10)
                            }
                        }
                    }
                    .padding(.bottom, geometry.safeAreaInsets.bottom > 0 ? 0 : 20) // 适配Home Indicator
                }
            }
            .navigationBarHidden(true)
        }
    }

    private func generateNumbers() {
        // 触觉反馈 - iPhone 14 Pro支持
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        isGenerating = true

        // 添加动画延迟效果
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.2) {
            let newNumbers: LotteryNumber

            switch selectedLotteryType {
            case .shuangSeQiu:
                newNumbers = generateShuangSeQiu()
            case .daLeTou:
                newNumbers = generateDaLeTou()
            case .kuaiLe8:
                newNumbers = generateKuaiLe8()
            case .liuHeCai:
                newNumbers = generateLiuHeCai()
            }

            // 成功生成的触觉反馈
            let successFeedback = UINotificationFeedbackGenerator()
            successFeedback.notificationOccurred(.success)

            withAnimation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0)) {
                currentNumbers = newNumbers
                generatedHistory.append(newNumbers)
                isGenerating = false
            }

            // 保存到数据库
            let newItem = Item(timestamp: Date())
            modelContext.insert(newItem)
        }
    }

    // MARK: - 各种彩票生成函数
    private func generateShuangSeQiu() -> LotteryNumber {
        // 生成红球 (1-33, 选6个不重复)
        var redBalls: [Int] = []
        while redBalls.count < 6 {
            let number = Int.random(in: 1...33)
            if !redBalls.contains(number) {
                redBalls.append(number)
            }
        }
        redBalls.sort()

        // 生成蓝球 (1-16, 选1个)
        let blueBall = Int.random(in: 1...16)

        return LotteryNumber(
            type: .shuangSeQiu,
            redBalls: redBalls,
            blueBall: blueBall,
            specialBalls: nil,
            timestamp: Date()
        )
    }

    private func generateDaLeTou() -> LotteryNumber {
        // 生成前区号码 (1-35, 选5个不重复)
        var frontBalls: [Int] = []
        while frontBalls.count < 5 {
            let number = Int.random(in: 1...35)
            if !frontBalls.contains(number) {
                frontBalls.append(number)
            }
        }
        frontBalls.sort()

        // 生成后区号码 (1-12, 选2个不重复)
        var backBalls: [Int] = []
        while backBalls.count < 2 {
            let number = Int.random(in: 1...12)
            if !backBalls.contains(number) {
                backBalls.append(number)
            }
        }
        backBalls.sort()

        return LotteryNumber(
            type: .daLeTou,
            redBalls: frontBalls,
            blueBall: nil,
            specialBalls: backBalls,
            timestamp: Date()
        )
    }

    private func generateKuaiLe8() -> LotteryNumber {
        // 生成快乐8号码 (1-80, 选10个不重复)
        var numbers: [Int] = []
        while numbers.count < 10 {
            let number = Int.random(in: 1...80)
            if !numbers.contains(number) {
                numbers.append(number)
            }
        }
        numbers.sort()

        return LotteryNumber(
            type: .kuaiLe8,
            redBalls: numbers,
            blueBall: nil,
            specialBalls: nil,
            timestamp: Date()
        )
    }

    private func generateLiuHeCai() -> LotteryNumber {
        // 生成六合彩号码 (1-49, 选6个不重复)
        var numbers: [Int] = []
        while numbers.count < 6 {
            let number = Int.random(in: 1...49)
            if !numbers.contains(number) {
                numbers.append(number)
            }
        }
        numbers.sort()

        // 生成特别号码 (1-49, 不能与前6个重复)
        var specialNumber: Int
        repeat {
            specialNumber = Int.random(in: 1...49)
        } while numbers.contains(specialNumber)

        return LotteryNumber(
            type: .liuHeCai,
            redBalls: numbers,
            blueBall: specialNumber,
            specialBalls: nil,
            timestamp: Date()
        )
    }

    // MARK: - 号码显示视图
    @ViewBuilder
    private func shuangSeQiuNumbersView(numbers: LotteryNumber, geometry: GeometryProxy) -> some View {
        VStack(spacing: 15) {
            // 红球显示 - 确保居中
            HStack(spacing: geometry.size.width > 400 ? 12 : 8) {
                ForEach(numbers.redBalls, id: \.self) { number in
                    ballView(number: number, color: .red, geometry: geometry)
                }
            }
            .frame(maxWidth: .infinity)

            // 蓝球显示 - 确保居中
            VStack(spacing: 8) {
                Text("蓝球")
                    .font(.headline)
                    .foregroundColor(.secondary)

                ballView(number: numbers.blueBall!, color: .blue, geometry: geometry)
            }
            .frame(maxWidth: .infinity)
        }
        .frame(maxWidth: .infinity)
    }

    @ViewBuilder
    private func daLeTouNumbersView(numbers: LotteryNumber, geometry: GeometryProxy) -> some View {
        VStack(spacing: 15) {
            // 前区号码
            VStack(spacing: 8) {
                Text("前区")
                    .font(.headline)
                    .foregroundColor(.secondary)

                HStack(spacing: geometry.size.width > 400 ? 12 : 8) {
                    ForEach(numbers.redBalls, id: \.self) { number in
                        ballView(number: number, color: .red, geometry: geometry)
                    }
                }
            }

            // 后区号码
            VStack(spacing: 8) {
                Text("后区")
                    .font(.headline)
                    .foregroundColor(.secondary)

                HStack(spacing: 10) {
                    ForEach(numbers.specialBalls!, id: \.self) { number in
                        ballView(number: number, color: .blue, geometry: geometry)
                    }
                }
            }
        }
    }

    @ViewBuilder
    private func kuaiLe8NumbersView(numbers: LotteryNumber, geometry: GeometryProxy) -> some View {
        VStack(spacing: 15) {
            Text("快乐8号码 (10个)")
                .font(.headline)
                .foregroundColor(.secondary)

            // 分2行显示，每行5个
            VStack(spacing: 8) {
                ForEach(0..<2, id: \.self) { row in
                    HStack(spacing: 8) {
                        ForEach(0..<5, id: \.self) { col in
                            let index = row * 5 + col
                            if index < numbers.redBalls.count {
                                ballView(number: numbers.redBalls[index], color: .orange, geometry: geometry, size: 40)
                            }
                        }
                    }
                    .frame(maxWidth: .infinity)
                }
            }
        }
        .frame(maxWidth: .infinity)
    }

    @ViewBuilder
    private func liuHeCaiNumbersView(numbers: LotteryNumber, geometry: GeometryProxy) -> some View {
        VStack(spacing: 15) {
            // 正常号码
            VStack(spacing: 8) {
                Text("正常号码")
                    .font(.headline)
                    .foregroundColor(.secondary)

                HStack(spacing: geometry.size.width > 400 ? 12 : 8) {
                    ForEach(numbers.redBalls, id: \.self) { number in
                        ballView(number: number, color: .green, geometry: geometry)
                    }
                }
                .frame(maxWidth: .infinity)
            }

            // 特别号码
            VStack(spacing: 8) {
                Text("特别号码")
                    .font(.headline)
                    .foregroundColor(.secondary)

                ballView(number: numbers.blueBall!, color: .purple, geometry: geometry)
            }
            .frame(maxWidth: .infinity)
        }
        .frame(maxWidth: .infinity)
    }

    @ViewBuilder
    private func ballView(number: Int, color: Color, geometry: GeometryProxy, size: CGFloat? = nil) -> some View {
        let ballSize = size ?? (geometry.size.width > 400 ? 50 : 45)
        let fontSize = size != nil ? size! * 0.4 : (geometry.size.width > 400 ? 18 : 16)

        Circle()
            .fill(
                LinearGradient(
                    gradient: Gradient(colors: [color.opacity(0.8), color]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .frame(width: ballSize, height: ballSize)
            .overlay(
                Text("\(number)")
                    .font(.system(size: fontSize, weight: .bold))
                    .foregroundColor(.white)
            )
            .shadow(color: color.opacity(0.3), radius: 3, x: 0, y: 2)
    }
}

struct HistoryRowView: View {
    let lottery: LotteryNumber
    let index: Int

    var body: some View {
        VStack(spacing: 8) {
            HStack {
                // 序号和类型
                VStack(alignment: .leading, spacing: 2) {
                    Text("#\(index)")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)

                    Text(lottery.type.rawValue)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                .frame(width: 50, alignment: .leading)

                Spacer()

                // 时间
                Text(lottery.timestamp, format: Date.FormatStyle(date: .omitted, time: .shortened))
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }

            // 根据彩票类型显示号码
            switch lottery.type {
            case .shuangSeQiu:
                shuangSeQiuHistoryView(lottery: lottery)
            case .daLeTou:
                daLeTouHistoryView(lottery: lottery)
            case .kuaiLe8:
                kuaiLe8HistoryView(lottery: lottery)
            case .liuHeCai:
                liuHeCaiHistoryView(lottery: lottery)
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }

    @ViewBuilder
    private func shuangSeQiuHistoryView(lottery: LotteryNumber) -> some View {
        HStack(spacing: 6) {
            // 红球
            ForEach(lottery.redBalls, id: \.self) { number in
                historyBallView(number: number, color: .red)
            }

            // 蓝球
            historyBallView(number: lottery.blueBall!, color: .blue)
        }
    }

    @ViewBuilder
    private func daLeTouHistoryView(lottery: LotteryNumber) -> some View {
        VStack(spacing: 4) {
            HStack(spacing: 4) {
                ForEach(lottery.redBalls, id: \.self) { number in
                    historyBallView(number: number, color: .red, size: 22)
                }
            }
            HStack(spacing: 4) {
                ForEach(lottery.specialBalls!, id: \.self) { number in
                    historyBallView(number: number, color: .blue, size: 22)
                }
            }
        }
    }

    @ViewBuilder
    private func kuaiLe8HistoryView(lottery: LotteryNumber) -> some View {
        VStack(spacing: 2) {
            ForEach(0..<2, id: \.self) { row in
                HStack(spacing: 2) {
                    ForEach(0..<5, id: \.self) { col in
                        let index = row * 5 + col
                        if index < lottery.redBalls.count {
                            historyBallView(number: lottery.redBalls[index], color: .orange, size: 18)
                        }
                    }
                }
            }
        }
    }

    @ViewBuilder
    private func liuHeCaiHistoryView(lottery: LotteryNumber) -> some View {
        HStack(spacing: 4) {
            ForEach(lottery.redBalls, id: \.self) { number in
                historyBallView(number: number, color: .green, size: 22)
            }
            historyBallView(number: lottery.blueBall!, color: .purple, size: 22)
        }
    }

    @ViewBuilder
    private func historyBallView(number: Int, color: Color, size: CGFloat = 25) -> some View {
        Circle()
            .fill(
                LinearGradient(
                    gradient: Gradient(colors: [color.opacity(0.8), color]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .frame(width: size, height: size)
            .overlay(
                Text("\(number)")
                    .font(.system(size: size * 0.4, weight: .bold))
                    .foregroundColor(.white)
            )
            .shadow(color: color.opacity(0.2), radius: 1, x: 0, y: 1)
    }
}

#Preview {
    ContentView()
        .modelContainer(for: Item.self, inMemory: true)
}

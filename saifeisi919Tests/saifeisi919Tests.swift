//
//  saifeisi919Tests.swift
//  saifeisi919Tests
//
//  Created by 赛飞斯 on 2025/9/19.
//

import Testing
import Foundation
@testable import saifeisi919

struct saifeisi919Tests {

    @Test func testLotteryNumberGeneration() async throws {
        // 测试双色球号码生成逻辑

        // 生成多组号码进行测试
        for _ in 1...100 {
            // 生成红球 (1-33, 选6个不重复)
            var redBalls: [Int] = []
            while redBalls.count < 6 {
                let number = Int.random(in: 1...33)
                if !redBalls.contains(number) {
                    redBalls.append(number)
                }
            }
            redBalls.sort()

            // 生成蓝球 (1-16, 选1个)
            let blueBall = Int.random(in: 1...16)

            // 验证红球
            #expect(redBalls.count == 6, "红球应该有6个")
            #expect(Set(redBalls).count == 6, "红球应该没有重复")

            for redBall in redBalls {
                #expect(redBall >= 1 && redBall <= 33, "红球应该在1-33范围内")
            }

            // 验证蓝球
            #expect(blueBall >= 1 && blueBall <= 16, "蓝球应该在1-16范围内")

            // 验证红球是否已排序
            #expect(redBalls == redBalls.sorted(), "红球应该按升序排列")
        }
    }

    @Test func testLotteryNumberStructure() async throws {
        // 测试LotteryNumber结构
        let redBalls = [1, 5, 12, 18, 25, 33]
        let blueBall = 8
        let timestamp = Date()

        let lottery = LotteryNumber(
            type: .shuangSeQiu,
            redBalls: redBalls,
            blueBall: blueBall,
            specialBalls: nil,
            timestamp: timestamp
        )

        #expect(lottery.type == .shuangSeQiu)
        #expect(lottery.redBalls == redBalls)
        #expect(lottery.blueBall == blueBall)
        #expect(lottery.specialBalls == nil)
        #expect(lottery.timestamp == timestamp)
    }

    @Test func testRedBallRange() async throws {
        // 测试红球范围
        for _ in 1...1000 {
            let number = Int.random(in: 1...33)
            #expect(number >= 1 && number <= 33, "红球应该在1-33范围内")
        }
    }

    @Test func testBlueBallRange() async throws {
        // 测试蓝球范围
        for _ in 1...1000 {
            let number = Int.random(in: 1...16)
            #expect(number >= 1 && number <= 16, "蓝球应该在1-16范围内")
        }
    }

    @Test func testDaLeTouGeneration() async throws {
        // 测试大乐透号码生成
        for _ in 1...100 {
            // 生成前区号码 (1-35, 选5个不重复)
            var frontBalls: [Int] = []
            while frontBalls.count < 5 {
                let number = Int.random(in: 1...35)
                if !frontBalls.contains(number) {
                    frontBalls.append(number)
                }
            }

            // 生成后区号码 (1-12, 选2个不重复)
            var backBalls: [Int] = []
            while backBalls.count < 2 {
                let number = Int.random(in: 1...12)
                if !backBalls.contains(number) {
                    backBalls.append(number)
                }
            }

            #expect(frontBalls.count == 5, "前区应该有5个号码")
            #expect(Set(frontBalls).count == 5, "前区号码应该没有重复")
            #expect(frontBalls.allSatisfy { $0 >= 1 && $0 <= 35 }, "前区号码应该在1-35范围内")

            #expect(backBalls.count == 2, "后区应该有2个号码")
            #expect(Set(backBalls).count == 2, "后区号码应该没有重复")
            #expect(backBalls.allSatisfy { $0 >= 1 && $0 <= 12 }, "后区号码应该在1-12范围内")
        }
    }

    @Test func testKuaiLe8Generation() async throws {
        // 测试快乐8号码生成
        for _ in 1...50 {
            // 生成快乐8号码 (1-80, 选10个不重复)
            var numbers: [Int] = []
            while numbers.count < 10 {
                let number = Int.random(in: 1...80)
                if !numbers.contains(number) {
                    numbers.append(number)
                }
            }

            #expect(numbers.count == 10, "快乐8应该有10个号码")
            #expect(Set(numbers).count == 10, "快乐8号码应该没有重复")
            #expect(numbers.allSatisfy { $0 >= 1 && $0 <= 80 }, "快乐8号码应该在1-80范围内")
        }
    }

    @Test func testLiuHeCaiGeneration() async throws {
        // 测试六合彩号码生成
        for _ in 1...100 {
            // 生成六合彩号码 (1-49, 选6个不重复)
            var numbers: [Int] = []
            while numbers.count < 6 {
                let number = Int.random(in: 1...49)
                if !numbers.contains(number) {
                    numbers.append(number)
                }
            }

            // 生成特别号码 (1-49, 不能与前6个重复)
            var specialNumber: Int
            repeat {
                specialNumber = Int.random(in: 1...49)
            } while numbers.contains(specialNumber)

            #expect(numbers.count == 6, "六合彩应该有6个正常号码")
            #expect(Set(numbers).count == 6, "六合彩正常号码应该没有重复")
            #expect(numbers.allSatisfy { $0 >= 1 && $0 <= 49 }, "六合彩正常号码应该在1-49范围内")
            #expect(specialNumber >= 1 && specialNumber <= 49, "六合彩特别号码应该在1-49范围内")
            #expect(!numbers.contains(specialNumber), "特别号码不能与正常号码重复")
        }
    }

}

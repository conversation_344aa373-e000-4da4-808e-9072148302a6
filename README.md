# 丰减壮生成器 iOS App

一个专为iPhone 14 Pro优化的多种彩票随机号码生成器应用，使用SwiftUI构建。

## 功能特点

### 🎲 多种彩票玩法
- **双色球**: 红球(1-33选6个) + 蓝球(1-16选1个)
- **大乐透**: 前区(1-35选5个) + 后区(1-12选2个)
- **快乐8**: 从1-80中选择20个不重复的数字
- **六合彩**: 正常号码(1-49选6个) + 特别号码(1个)
- **一键生成**: 选择彩票类型后点击按钮即可生成完整号码

### 📱 iPhone 14 Pro 优化
- **Dynamic Island适配**: 针对iPhone 14 Pro的Dynamic Island进行了界面优化
- **屏幕尺寸适配**: 根据屏幕宽度动态调整球体大小和间距
- **触觉反馈**: 利用iPhone的触觉引擎提供按钮点击和生成成功的反馈
- **现代设计**: 使用渐变色彩和阴影效果，提供现代化的视觉体验

### 🎨 界面设计
- **彩票类型选择**: 顶部分段控制器可切换不同彩票玩法
- **美观的球体显示**: 不同彩票类型使用不同颜色的渐变球体
- **动画效果**: 生成过程中的加载动画和按钮缩放效果
- **历史记录**: 显示生成的历史号码，支持不同彩票类型的混合记录
- **响应式布局**: 界面居中对齐，适配不同屏幕尺寸和方向

### 📊 历史记录
- **多类型记录**: 保存所有彩票类型的生成号码和时间戳
- **类型标识**: 每组号码都标明彩票类型
- **序号标记**: 每组号码都有唯一的序号
- **时间显示**: 显示每组号码的生成时间
- **统计信息**: 显示总共生成的注数

## 技术特性

### 🛠 技术栈
- **SwiftUI**: 现代化的用户界面框架
- **SwiftData**: 数据持久化存储
- **Swift Testing**: 使用最新的Swift Testing框架进行单元测试
- **iOS 18.5+**: 支持最新的iOS功能

### 🧪 测试覆盖
- **多彩票类型测试**: 验证双色球、大乐透、快乐8、六合彩的生成逻辑
- **号码范围验证**: 确保各种彩票类型的号码在正确范围内
- **唯一性测试**: 验证生成号码的不重复性
- **数据结构测试**: 验证LotteryNumber结构的正确性
- **性能测试**: 测试大量号码生成的性能表现

### 🎯 代码质量
- **模块化设计**: 清晰的代码结构和组件分离
- **错误处理**: 完善的错误处理机制
- **内存管理**: 优化的内存使用和生命周期管理
- **可维护性**: 易于理解和维护的代码结构

## 安装和运行

### 系统要求
- iOS 18.5 或更高版本
- iPhone 14 Pro 或兼容设备
- Xcode 16.0 或更高版本（开发）

### 构建步骤
1. 克隆项目到本地
2. 使用Xcode打开 `saifeisi919.xcodeproj`
3. 选择iPhone 16 Pro模拟器或真机
4. 点击运行按钮构建和安装应用

### 运行测试
```bash
xcodebuild test -project saifeisi919.xcodeproj -scheme saifeisi919 -destination 'platform=iOS Simulator,name=iPhone 16 Pro'
```

## 使用说明

1. **启动应用**: 打开丰减壮生成器应用
2. **选择彩票类型**: 使用顶部的分段控制器选择想要的彩票玩法
3. **生成号码**: 点击"生成[彩票类型]"按钮
4. **查看结果**: 应用会根据选择的类型显示相应的号码
5. **查看历史**: 向下滚动查看之前生成的所有类型号码
6. **切换类型**: 可以随时切换不同的彩票类型并生成新号码

## 项目结构

```
saifeisi919/
├── saifeisi919/
│   ├── ContentView.swift          # 主界面视图
│   ├── LaunchScreen.swift         # 启动屏幕
│   ├── saifeisi919App.swift       # 应用入口
│   ├── Item.swift                 # 数据模型
│   └── Assets.xcassets/           # 资源文件
├── saifeisi919Tests/
│   └── saifeisi919Tests.swift     # 单元测试
└── saifeisi919UITests/            # UI测试
```

## 开发者信息

- **开发者**: 赛飞斯
- **应用名称**: 丰减壮生成器
- **创建日期**: 2025年9月19日
- **版本**: 1.0
- **平台**: iOS 18.5+

## 许可证

本项目仅供学习和个人使用。

---

**注意**: 本应用生成的号码仅供娱乐参考，不构成任何投注建议。请理性对待彩票，适度娱乐。
